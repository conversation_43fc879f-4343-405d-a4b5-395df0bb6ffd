import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
  TestTube, Shield, GitBranch, Droplets, Atom, Package2, Cog,
  TrendingUp, TrendingDown, Minus, WifiOff
} from 'lucide-react';

// Ham madde listesi
const hammaddeler = [
  { id: 'STAB1', name: 'STAB1', icon: TestTube, color: 'text-blue-600', bgColor: 'bg-blue-100' },
  { id: 'STAB2', name: 'STAB2', icon: TestTube, color: 'text-blue-600', bgColor: 'bg-blue-100' },
  { id: 'MUKAVEMET', name: 'MUKAV.', icon: Shield, color: 'text-red-600', bgColor: 'bg-red-100' },
  { id: 'PROSES', name: 'PROSES', icon: GitBranch, color: 'text-purple-600', bgColor: 'bg-purple-100' },
  { id: 'ENJEKSIYON', name: 'ENJEK.', icon: Droplets, color: 'text-cyan-600', bgColor: 'bg-cyan-100' },
  { id: 'KALSIT', name: 'KALSİT', icon: Atom, color: 'text-orange-600', bgColor: 'bg-orange-100' },
  { id: 'PVC', name: 'PVC', icon: Package2, color: 'text-green-600', bgColor: 'bg-green-100' },
  { id: 'MOT12', name: 'MOT12', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT13', name: 'MOT13', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT14', name: 'MOT14', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT15', name: 'MOT15', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT16', name: 'MOT16', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT17', name: 'MOT17', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT18', name: 'MOT18', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
];

interface HammaddeData {
  id: string;
  anlik: number;
  set: number;
  stok: number;
  gunlukToplam: number;
  birim: string;
}

interface HammaddeAnlikRecord {
  ID: number;
  Tarih: string;
  DozajNo: string;
  STAB1: number | string;
  STAB2: number | string;
  MUKAVEMET: number | string;
  PROSES: number | string;
  ENJEKSIYON: number | string;
  KALSIT: number | string;
  PVC: number | string;
  MOT12: number | string;
  MOT13: number | string;
  MOT14: number | string;
  MOT15: number | string;
  MOT16: number | string;
  MOT17: number | string;
  MOT18: number | string;
}

export const HammaddeVeriler = () => {
  const [hammaddeData, setHammaddeData] = useState<HammaddeData[]>([]);
  const [anlikRecords, setAnlikRecords] = useState<HammaddeAnlikRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [connectionError, setConnectionError] = useState(false);
  const [lastAnlikUpdate, setLastAnlikUpdate] = useState<string>('');

  // Gerçek API'den veri çek
  useEffect(() => {
    const fetchHammaddeData = async () => {
      try {
        setLoading(true);
        setConnectionError(false);

        // Paralel olarak tüm ham madde verilerini çek
        const [anlikResponse, setResponse, stokResponse, gunlukToplamResponse, anlikListeResponse] = await Promise.all([
          fetch('/api/hammadde/anlik'),
          fetch('/api/hammadde/set'),
          fetch('/api/hammadde/stok'),
          fetch('/api/hammadde/gunluk-toplam'),
          fetch('/api/hammadde/anlik-liste')
        ]);

        // Response'ların başarılı olup olmadığını kontrol et
        if (!anlikResponse.ok || !setResponse.ok || !stokResponse.ok || !gunlukToplamResponse.ok || !anlikListeResponse.ok) {
          throw new Error('API yanıt hatası');
        }

        const anlikData = await anlikResponse.json();
        const setData = await setResponse.json();
        const stokData = await stokResponse.json();
        const gunlukToplamData = await gunlukToplamResponse.json();
        const anlikListeData = await anlikListeResponse.json();

        // Verileri birleştir - tablolar sütun bazlı yapıda
        const combinedData: HammaddeData[] = hammaddeler.map(h => {
          const anlik = anlikData.length > 0 ? anlikData[0][h.id] || 0 : 0;
          const set = setData.length > 0 ? setData[0][h.id] || 0 : 0;
          const stok = stokData.length > 0 ? stokData[0][h.id] || 0 : 0;
          const gunlukToplam = gunlukToplamData.length > 0 ? gunlukToplamData[0][h.id] || 0 : 0;

          return {
            id: h.id,
            anlik: parseFloat(anlik),
            set: parseFloat(set),
            stok: parseFloat(stok),
            gunlukToplam: parseFloat(gunlukToplam),
            birim: 'kg'
          };
        });

        setHammaddeData(combinedData);
        setAnlikRecords(anlikListeData);

        // En son HammaddeAnlik güncellemesini takip et
        if (anlikListeData.length > 0) {
          const latestRecord = anlikListeData[0];
          const updateKey = `${latestRecord.Tarih}-${latestRecord.DozajNo}`;
          setLastAnlikUpdate(updateKey);
        }
      } catch (error) {
        console.error('Ham madde verileri çekilemedi:', error);
        setConnectionError(true);
        setHammaddeData([]); // Boş array set et
      } finally {
        setLoading(false);
      }
    };

    fetchHammaddeData();

    // 30 saniyede bir verileri güncelle
    const interval = setInterval(fetchHammaddeData, 30000);

    return () => clearInterval(interval);
  }, []);

  // HammaddeAnlik güncellendiğinde HammaddeStok verilerini yeniden çek
  useEffect(() => {
    if (!lastAnlikUpdate || loading) return;

    const updateStokData = async () => {
      try {
        const stokResponse = await fetch('/api/hammadde/stok');
        if (!stokResponse.ok) return;

        const stokData = await stokResponse.json();

        // Mevcut hammaddeData'yı güncelle - sadece stok değerlerini
        setHammaddeData(prevData =>
          prevData.map(item => {
            const newStok = stokData.length > 0 ? stokData[0][item.id] || 0 : 0;
            return {
              ...item,
              stok: parseFloat(newStok)
            };
          })
        );
      } catch (error) {
        console.error('Stok verileri güncellenemedi:', error);
      }
    };

    updateStokData();
  }, [lastAnlikUpdate, loading]);

  const getTrendIcon = (anlik: number, set: number) => {
    const diff = anlik - set;
    if (Math.abs(diff) < 10) return <Minus className="h-3 w-3 text-gray-500" />;
    return diff > 0 ?
      <TrendingUp className="h-3 w-3 text-green-500" /> :
      <TrendingDown className="h-3 w-3 text-red-500" />;
  };

  const getStokDurumu = (stok: number) => {
    if (stok < 1000) return { label: 'Düşük', variant: 'destructive' as const };
    if (stok < 3000) return { label: 'Orta', variant: 'secondary' as const };
    return { label: 'Yüksek', variant: 'default' as const };
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="grid gap-x-1 gap-y-2 grid-cols-14">
          {Array.from({ length: 14 }).map((_, i) => (
            <Card key={i} className="animate-pulse min-w-0">
              <CardHeader className="pb-1 px-1 pt-1">
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent className="px-1 pb-1">
                <div className="space-y-1">
                  <div className="h-2 bg-gray-200 rounded"></div>
                  <div className="h-2 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Bağlantı hatası durumunda özel görünüm
  if (connectionError) {
    return (
      <div className="space-y-4">
        <div className="grid gap-x-1 gap-y-2 grid-cols-14">
          {hammaddeler.map((hammadde) => (
            <Card key={hammadde.id} className="border-red-200 bg-red-50 min-w-0">
              <CardHeader className="pb-1 px-1 pt-1">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    <div className={`p-0.5 rounded-sm ${hammadde.bgColor} opacity-50`}>
                      <hammadde.icon className={`h-2 w-2 ${hammadde.color}`} />
                    </div>
                    <CardTitle className="text-xs font-medium truncate text-gray-500">{hammadde.name}</CardTitle>
                  </div>
                  <div className="flex-shrink-0">
                    <WifiOff className="h-2 w-2 text-red-500" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-1 px-1 pb-1">
                <div className="flex items-center justify-center py-1">
                  <div className="text-xs text-red-500 text-center">
                    Bağlantı Hatası
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Ham madde kartları */}
      <div className="grid gap-x-1 gap-y-2 grid-cols-14">
        {hammaddeler.map((hammadde) => {
          const data = hammaddeData.find(d => d.id === hammadde.id);

          return (
            <Card key={hammadde.id} className="hover:shadow-md transition-shadow min-w-0">
              <CardHeader className="pb-1 px-1 pt-1">
                <div className="flex items-center justify-center">
                  <div className="flex items-center gap-1">
                    <div className={`p-0.5 rounded-sm ${hammadde.bgColor}`}>
                      <hammadde.icon className={`h-2 w-2 ${hammadde.color}`} />
                    </div>
                    <CardTitle className="text-xs font-medium truncate">{hammadde.name}</CardTitle>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-1 px-1 pb-1">
                {data ? (
                  <>
                    <div className="text-center">
                      <div>
                        <span className="text-muted-foreground text-xs">SET:</span>
                        <div className="font-semibold text-blue-600 text-xs">
                          {data.set.toFixed(1)}
                        </div>
                      </div>
                    </div>
                    <div className="text-center">
                      <div>
                        <span className="text-muted-foreground text-xs">STOK:</span>
                        <div className="font-semibold text-xs">
                          {data.stok.toFixed(0)}
                        </div>
                      </div>
                    </div>
                    <div className="text-center">
                      <div>
                        <span className="text-muted-foreground text-xs">GÜNLÜK:</span>
                        <div className="font-semibold text-purple-600 text-xs">
                          {data.gunlukToplam.toFixed(1)}
                        </div>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="text-xs text-muted-foreground text-center">
                    Yükleniyor...
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Ham madde anlık veriler tablosu */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="max-h-96 overflow-y-auto overflow-x-auto">
          <Table className="text-xs">
            <TableHeader className="sticky top-0 bg-white">
              <TableRow>
                <TableHead className="text-xs px-2 py-1 min-w-[80px]">Tarih</TableHead>
                <TableHead className="text-xs px-2 py-1 min-w-[60px]">Dozaj</TableHead>
                {hammaddeler.map((hammadde) => (
                  <TableHead key={hammadde.id} className="text-xs px-1 py-1 min-w-[50px]">
                    <div className="flex items-center gap-0.5 justify-center">
                      <div className={`p-0.5 rounded-sm ${hammadde.bgColor}`}>
                        <hammadde.icon className={`h-2 w-2 ${hammadde.color}`} />
                      </div>
                      <span className="font-medium text-xs">{hammadde.name}</span>
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {anlikRecords.map((record) => (
                <TableRow key={record.ID} className="hover:bg-gray-50">
                  <TableCell className="text-xs px-2 py-1">
                    {new Date(record.Tarih).toLocaleDateString('tr-TR', {
                      day: '2-digit',
                      month: '2-digit',
                      year: '2-digit'
                    })} {new Date(record.Tarih).toLocaleTimeString('tr-TR', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </TableCell>
                  <TableCell className="text-xs px-2 py-1">{record.DozajNo}</TableCell>
                  <TableCell className="text-xs px-1 py-1 text-center">{parseFloat(record.STAB1 || 0).toFixed(1)}</TableCell>
                  <TableCell className="text-xs px-1 py-1 text-center">{parseFloat(record.STAB2 || 0).toFixed(1)}</TableCell>
                  <TableCell className="text-xs px-1 py-1 text-center">{parseFloat(record.MUKAVEMET || 0).toFixed(1)}</TableCell>
                  <TableCell className="text-xs px-1 py-1 text-center">{parseFloat(record.PROSES || 0).toFixed(1)}</TableCell>
                  <TableCell className="text-xs px-1 py-1 text-center">{parseFloat(record.ENJEKSIYON || 0).toFixed(1)}</TableCell>
                  <TableCell className="text-xs px-1 py-1 text-center">{parseFloat(record.KALSIT || 0).toFixed(1)}</TableCell>
                  <TableCell className="text-xs px-1 py-1 text-center">{parseFloat(record.PVC || 0).toFixed(1)}</TableCell>
                  <TableCell className="text-xs px-1 py-1 text-center">{parseFloat(record.MOT12 || 0).toFixed(1)}</TableCell>
                  <TableCell className="text-xs px-1 py-1 text-center">{parseFloat(record.MOT13 || 0).toFixed(1)}</TableCell>
                  <TableCell className="text-xs px-1 py-1 text-center">{parseFloat(record.MOT14 || 0).toFixed(1)}</TableCell>
                  <TableCell className="text-xs px-1 py-1 text-center">{parseFloat(record.MOT15 || 0).toFixed(1)}</TableCell>
                  <TableCell className="text-xs px-1 py-1 text-center">{parseFloat(record.MOT16 || 0).toFixed(1)}</TableCell>
                  <TableCell className="text-xs px-1 py-1 text-center">{parseFloat(record.MOT17 || 0).toFixed(1)}</TableCell>
                  <TableCell className="text-xs px-1 py-1 text-center">{parseFloat(record.MOT18 || 0).toFixed(1)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};
