import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
  TestTube, Shield, GitBranch, Droplets, Atom, Package2, Cog,
  TrendingUp, TrendingDown, Minus, WifiOff
} from 'lucide-react';

// Ham madde listesi
const hammaddeler = [
  { id: 'STAB1', name: 'STAB1', icon: TestTube, color: 'text-blue-600', bgColor: 'bg-blue-100' },
  { id: 'STAB2', name: 'STAB2', icon: TestTube, color: 'text-blue-600', bgColor: 'bg-blue-100' },
  { id: 'MUKAVEMET', name: 'MUKAVEMET', icon: Shield, color: 'text-red-600', bgColor: 'bg-red-100' },
  { id: 'PROSES', name: 'PROSES', icon: GitBranch, color: 'text-purple-600', bgColor: 'bg-purple-100' },
  { id: 'ENJEKSIYON', name: 'ENJEKSIYON', icon: Droplets, color: 'text-cyan-600', bgColor: 'bg-cyan-100' },
  { id: 'KALSIT', name: 'KALSİT', icon: Atom, color: 'text-orange-600', bgColor: 'bg-orange-100' },
  { id: 'PVC', name: 'PVC', icon: Package2, color: 'text-green-600', bgColor: 'bg-green-100' },
  { id: 'MOT12', name: 'MOT12', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT13', name: 'MOT13', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT14', name: 'MOT14', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT15', name: 'MOT15', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT16', name: 'MOT16', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT17', name: 'MOT17', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
  { id: 'MOT18', name: 'MOT18', icon: Cog, color: 'text-slate-600', bgColor: 'bg-slate-100' },
];

interface HammaddeData {
  id: string;
  anlik: number;
  set: number;
  stok: number;
  gunlukToplam: number;
  birim: string;
}

interface HammaddeAnlikRecord {
  ID: number;
  Tarih: string;
  DozajNo: string;
  STAB1: number;
  STAB2: number;
  MUKAVEMET: number;
  PROSES: number;
  ENJEKSIYON: number;
  KALSIT: number;
  PVC: number;
  MOT12: number;
  MOT13: number;
  MOT14: number;
  MOT15: number;
  MOT16: number;
  MOT17: number;
  MOT18: number;
}

export const HammaddeVeriler = () => {
  const [hammaddeData, setHammaddeData] = useState<HammaddeData[]>([]);
  const [anlikRecords, setAnlikRecords] = useState<HammaddeAnlikRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [connectionError, setConnectionError] = useState(false);

  // Gerçek API'den veri çek
  useEffect(() => {
    const fetchHammaddeData = async () => {
      try {
        setLoading(true);
        setConnectionError(false);

        // Paralel olarak tüm ham madde verilerini çek
        const [anlikResponse, setResponse, stokResponse, gunlukToplamResponse, anlikListeResponse] = await Promise.all([
          fetch('/api/hammadde/anlik'),
          fetch('/api/hammadde/set'),
          fetch('/api/hammadde/stok'),
          fetch('/api/hammadde/gunluk-toplam'),
          fetch('/api/hammadde/anlik-liste')
        ]);

        // Response'ların başarılı olup olmadığını kontrol et
        if (!anlikResponse.ok || !setResponse.ok || !stokResponse.ok || !gunlukToplamResponse.ok || !anlikListeResponse.ok) {
          throw new Error('API yanıt hatası');
        }

        const anlikData = await anlikResponse.json();
        const setData = await setResponse.json();
        const stokData = await stokResponse.json();
        const gunlukToplamData = await gunlukToplamResponse.json();
        const anlikListeData = await anlikListeResponse.json();

        // Verileri birleştir - tablolar sütun bazlı yapıda
        const combinedData: HammaddeData[] = hammaddeler.map(h => {
          const anlik = anlikData.length > 0 ? anlikData[0][h.id] || 0 : 0;
          const set = setData.length > 0 ? setData[0][h.id] || 0 : 0;
          const stok = stokData.length > 0 ? stokData[0][h.id] || 0 : 0;
          const gunlukToplam = gunlukToplamData.length > 0 ? gunlukToplamData[0][h.id] || 0 : 0;

          return {
            id: h.id,
            anlik: parseFloat(anlik),
            set: parseFloat(set),
            stok: parseFloat(stok),
            gunlukToplam: parseFloat(gunlukToplam),
            birim: 'kg'
          };
        });

        setHammaddeData(combinedData);
        setAnlikRecords(anlikListeData);
      } catch (error) {
        console.error('Ham madde verileri çekilemedi:', error);
        setConnectionError(true);
        setHammaddeData([]); // Boş array set et
      } finally {
        setLoading(false);
      }
    };

    fetchHammaddeData();

    // 30 saniyede bir verileri güncelle
    const interval = setInterval(fetchHammaddeData, 30000);

    return () => clearInterval(interval);
  }, []);

  const getTrendIcon = (anlik: number, set: number) => {
    const diff = anlik - set;
    if (Math.abs(diff) < 10) return <Minus className="h-3 w-3 text-gray-500" />;
    return diff > 0 ?
      <TrendingUp className="h-3 w-3 text-green-500" /> :
      <TrendingDown className="h-3 w-3 text-red-500" />;
  };

  const getStokDurumu = (stok: number) => {
    if (stok < 1000) return { label: 'Düşük', variant: 'destructive' as const };
    if (stok < 3000) return { label: 'Orta', variant: 'secondary' as const };
    return { label: 'Yüksek', variant: 'default' as const };
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-x-2 gap-y-4 grid-cols-14">
          {Array.from({ length: 14 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-1 px-2 pt-2">
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent className="px-2 pb-2">
                <div className="space-y-1">
                  <div className="h-2 bg-gray-200 rounded"></div>
                  <div className="h-2 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Bağlantı hatası durumunda özel görünüm
  if (connectionError) {
    return (
      <div className="space-y-6">
        <div className="grid gap-x-2 gap-y-4 grid-cols-14">
          {hammaddeler.map((hammadde) => (
            <Card key={hammadde.id} className="border-red-200 bg-red-50">
              <CardHeader className="pb-1 px-2 pt-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    <div className={`p-1 rounded-md ${hammadde.bgColor} opacity-50`}>
                      <hammadde.icon className={`h-2 w-2 ${hammadde.color}`} />
                    </div>
                    <CardTitle className="text-xs font-medium truncate text-gray-500">{hammadde.name}</CardTitle>
                  </div>
                  <div className="flex-shrink-0">
                    <WifiOff className="h-2 w-2 text-red-500" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-1 px-2 pb-2">
                <div className="flex items-center justify-center py-1">
                  <div className="text-xs text-red-500 text-center">
                    Bağlantı Hatası
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Ham madde kartları */}
      <div className="grid gap-x-2 gap-y-4 grid-cols-14">
        {hammaddeler.map((hammadde) => {
          const data = hammaddeData.find(d => d.id === hammadde.id);
          const stokDurumu = data ? getStokDurumu(data.stok) : { label: 'Bilinmiyor', variant: 'secondary' as const };

          return (
            <Card key={hammadde.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-1 px-2 pt-2">
                <div className="flex items-center justify-center">
                  <div className="flex items-center gap-1">
                    <div className={`p-1 rounded-md ${hammadde.bgColor}`}>
                      <hammadde.icon className={`h-2 w-2 ${hammadde.color}`} />
                    </div>
                    <CardTitle className="text-xs font-medium truncate">{hammadde.name}</CardTitle>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-1 px-2 pb-2">
                {data ? (
                  <>
                    <div className="text-center">
                      <div>
                        <span className="text-muted-foreground text-xs">SET:</span>
                        <div className="font-semibold text-blue-600 text-xs">
                          {data.set.toFixed(1)} kg
                        </div>
                      </div>
                    </div>
                    <div className="text-center">
                      <div>
                        <span className="text-muted-foreground text-xs">STOK:</span>
                        <div className="font-semibold text-xs">
                          {data.stok.toFixed(0)} kg
                        </div>
                      </div>
                    </div>
                    <div className="text-center">
                      <div>
                        <span className="text-muted-foreground text-xs">GÜNLÜK:</span>
                        <div className="font-semibold text-purple-600 text-xs">
                          {data.gunlukToplam.toFixed(1)} kg
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-center">
                      <Badge variant={stokDurumu.variant} className="text-xs px-1 py-0 h-4">
                        {stokDurumu.label}
                      </Badge>
                    </div>
                  </>
                ) : (
                  <div className="text-xs text-muted-foreground text-center">
                    Yükleniyor...
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Ham madde anlık veriler tablosu */}
      <div className="bg-white rounded-lg border">
        <div className="p-4 border-b">
          <h3 className="text-lg font-semibold">Ham Madde Anlık Veriler</h3>
          <p className="text-sm text-muted-foreground">En yeni tarihten başlayarak sıralanmış veriler</p>
        </div>
        <div className="max-h-96 overflow-y-auto">
          <Table>
            <TableHeader className="sticky top-0 bg-white">
              <TableRow>
                <TableHead className="text-xs">Tarih</TableHead>
                <TableHead className="text-xs">Dozaj No</TableHead>
                <TableHead className="text-xs">STAB1</TableHead>
                <TableHead className="text-xs">STAB2</TableHead>
                <TableHead className="text-xs">MUKAVEMET</TableHead>
                <TableHead className="text-xs">PROSES</TableHead>
                <TableHead className="text-xs">ENJEKSIYON</TableHead>
                <TableHead className="text-xs">KALSİT</TableHead>
                <TableHead className="text-xs">PVC</TableHead>
                <TableHead className="text-xs">MOT12</TableHead>
                <TableHead className="text-xs">MOT13</TableHead>
                <TableHead className="text-xs">MOT14</TableHead>
                <TableHead className="text-xs">MOT15</TableHead>
                <TableHead className="text-xs">MOT16</TableHead>
                <TableHead className="text-xs">MOT17</TableHead>
                <TableHead className="text-xs">MOT18</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {anlikRecords.map((record) => (
                <TableRow key={record.ID} className="hover:bg-gray-50">
                  <TableCell className="text-xs">{new Date(record.Tarih).toLocaleDateString('tr-TR')}</TableCell>
                  <TableCell className="text-xs">{record.DozajNo}</TableCell>
                  <TableCell className="text-xs">{record.STAB1?.toFixed(1) || '0.0'}</TableCell>
                  <TableCell className="text-xs">{record.STAB2?.toFixed(1) || '0.0'}</TableCell>
                  <TableCell className="text-xs">{record.MUKAVEMET?.toFixed(1) || '0.0'}</TableCell>
                  <TableCell className="text-xs">{record.PROSES?.toFixed(1) || '0.0'}</TableCell>
                  <TableCell className="text-xs">{record.ENJEKSIYON?.toFixed(1) || '0.0'}</TableCell>
                  <TableCell className="text-xs">{record.KALSIT?.toFixed(1) || '0.0'}</TableCell>
                  <TableCell className="text-xs">{record.PVC?.toFixed(1) || '0.0'}</TableCell>
                  <TableCell className="text-xs">{record.MOT12?.toFixed(1) || '0.0'}</TableCell>
                  <TableCell className="text-xs">{record.MOT13?.toFixed(1) || '0.0'}</TableCell>
                  <TableCell className="text-xs">{record.MOT14?.toFixed(1) || '0.0'}</TableCell>
                  <TableCell className="text-xs">{record.MOT15?.toFixed(1) || '0.0'}</TableCell>
                  <TableCell className="text-xs">{record.MOT16?.toFixed(1) || '0.0'}</TableCell>
                  <TableCell className="text-xs">{record.MOT17?.toFixed(1) || '0.0'}</TableCell>
                  <TableCell className="text-xs">{record.MOT18?.toFixed(1) || '0.0'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};
